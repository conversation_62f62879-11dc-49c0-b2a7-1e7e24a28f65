import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { Trash2, <PERSON>ader2, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { trpcClient, queryClient } from "@/utils/trpc";
import type { Stock } from "../table/table-columns";

interface DeleteStockDialogProps {
  stock: Stock;
  onSuccess?: () => void;
  children?: React.ReactNode;
}

export function DeleteStockDialog({ stock, onSuccess, children }: DeleteStockDialogProps) {
  const [open, setOpen] = useState(false);

  const deleteStockMutation = useMutation({
    mutationFn: async () => {
      return trpcClient.stock.delete.mutate({ id: stock.id });
    },
    onSuccess: () => {
      toast.success("Stock deleted successfully!");
      setOpen(false);
      
      // Invalidate and refetch stocks
      queryClient.invalidateQueries({
        queryKey: [["stock", "getAll"]],
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete stock");
    },
  });

  // Check if stock can be deleted (only AVAILABLE stocks can be deleted)
  const canDelete = stock.status === "AVAILABLE";
  const isAssignedOrSold = stock.status === "ASSIGNED" || stock.status === "SOLD";

  const trigger = children || (
    <Button
      variant="ghost"
      size="sm"
      disabled={!canDelete}
      className="h-8 w-8 p-0 text-destructive hover:text-destructive"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  );

  if (!canDelete) {
    return (
      <div className="relative">
        {trigger}
        <span className="sr-only">Cannot delete {stock.status.toLowerCase()} stock</span>
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Delete Stock
          </DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this stock item? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Stock Details */}
          <div className="rounded-lg border p-4 space-y-2">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-medium">Stock ID:</span>
                <p className="text-muted-foreground">{stock.id}</p>
              </div>
              <div>
                <span className="font-medium">Account Name:</span>
                <p className="text-muted-foreground">{stock.name}</p>
              </div>
              <div>
                <span className="font-medium">Email:</span>
                <p className="text-muted-foreground">{stock.email}</p>
              </div>
              <div>
                <span className="font-medium">Status:</span>
                <p className="text-muted-foreground">{stock.status}</p>
              </div>
              <div className="col-span-2">
                <span className="font-medium">Product:</span>
                <p className="text-muted-foreground">
                  {stock.variant.product.name} - {stock.variant.name}
                </p>
              </div>
            </div>
          </div>

          {/* Warning for assigned/sold stocks */}
          {isAssignedOrSold && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This stock item cannot be deleted because it has been {stock.status.toLowerCase()}.
                Only available stock items can be deleted.
              </AlertDescription>
            </Alert>
          )}

          {/* Warning for available stocks */}
          {canDelete && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This will permanently delete the stock item and all its data.
                This action cannot be undone.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={deleteStockMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={() => deleteStockMutation.mutate()}
            disabled={deleteStockMutation.isPending || !canDelete}
          >
            {deleteStockMutation.isPending && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
            Delete Stock
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
