import { useState, useRef } from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { Upload, FileText, Loader2, Download, AlertCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { trpc, trpcClient, queryClient } from "@/utils/trpc";
import { Alert, AlertDescription } from "@/components/ui/alert";

// CSV row validation schema
const csvRowSchema = z.object({
  name: z.string().min(1, "Account name is required"),
  email: z.string().email("Valid email is required"),
  password: z.string().min(1, "Password is required"),
});

type CsvRow = z.infer<typeof csvRowSchema>;

interface BulkUploadDialogProps {
  onSuccess?: () => void;
}

interface ParsedCsvData {
  data: CsvRow[];
  errors: Array<{ row: number; errors: string[] }>;
}

export function BulkUploadDialog({ onSuccess }: BulkUploadDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  const [selectedVariantId, setSelectedVariantId] = useState<string>("");
  const [csvData, setCsvData] = useState<ParsedCsvData | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch products for dropdown
  const { data: productsData, isLoading: isLoadingProducts } = useQuery(
    trpc.product.getAll.queryOptions({
      page: 1,
      limit: 100, // Get all products for dropdown
    })
  );

  // Fetch variants based on selected product
  const { data: variants, isLoading: isLoadingVariants } = useQuery(
    trpc.variant.getByProductId.queryOptions(
      { productId: selectedProductId },
      { enabled: !!selectedProductId }
    )
  );

  const createBulkStockMutation = useMutation({
    mutationFn: async (data: { variantId: string; stocks: CsvRow[] }) => {
      return trpcClient.stock.createBulk.mutate(data);
    },
    onSuccess: (result) => {
      toast.success(`Successfully created ${result.created} stock items!`);
      setOpen(false);
      resetForm();

      // Invalidate and refetch stocks
      queryClient.invalidateQueries({
        queryKey: [["stock", "getAll"]],
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create stock items");
    },
  });

  const resetForm = () => {
    setSelectedProductId("");
    setSelectedVariantId("");
    setCsvData(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const parseCsvFile = (file: File): Promise<ParsedCsvData> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const text = e.target?.result as string;
        const lines = text.split("\n").filter((line) => line.trim());

        if (lines.length < 2) {
          resolve({
            data: [],
            errors: [
              {
                row: 0,
                errors: [
                  "File must contain at least a header and one data row",
                ],
              },
            ],
          });
          return;
        }

        const headers = lines[0].split(",").map((h) => h.trim().toLowerCase());
        const expectedHeaders = ["name", "email", "password"];

        // Check if all required headers are present
        const missingHeaders = expectedHeaders.filter(
          (h) => !headers.includes(h)
        );
        if (missingHeaders.length > 0) {
          resolve({
            data: [],
            errors: [
              {
                row: 0,
                errors: [
                  `Missing required columns: ${missingHeaders.join(", ")}`,
                ],
              },
            ],
          });
          return;
        }

        const data: CsvRow[] = [];
        const errors: Array<{ row: number; errors: string[] }> = [];

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(",").map((v) => v.trim());
          const rowData: any = {};

          headers.forEach((header, index) => {
            if (expectedHeaders.includes(header)) {
              rowData[header] = values[index] || "";
            }
          });

          try {
            const validatedRow = csvRowSchema.parse(rowData);
            data.push(validatedRow);
          } catch (error) {
            if (error instanceof z.ZodError) {
              errors.push({
                row: i + 1,
                errors: error.issues.map((issue: z.ZodIssue) => issue.message),
              });
            }
          }
        }

        resolve({ data, errors });
      };
      reader.readAsText(file);
    });
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file type
    const allowedTypes = [".csv", ".txt"];
    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      toast.error("Please upload a CSV file");
      return;
    }

    setIsUploading(true);
    try {
      const parsedData = await parseCsvFile(file);
      setCsvData(parsedData);

      if (parsedData.errors.length > 0) {
        toast.warning(
          `File parsed with ${parsedData.errors.length} errors. Please review before proceeding.`
        );
      } else {
        toast.success(`Successfully parsed ${parsedData.data.length} rows`);
      }
    } catch (error) {
      toast.error("Failed to parse CSV file");
    } finally {
      setIsUploading(false);
    }
  };

  const handleConfirmUpload = async () => {
    if (!selectedVariantId || !csvData?.data.length) {
      toast.error("Please select a variant and upload valid data");
      return;
    }

    if (csvData.errors.length > 0) {
      toast.error("Please fix all errors before proceeding");
      return;
    }

    await createBulkStockMutation.mutateAsync({
      variantId: selectedVariantId,
      stocks: csvData.data,
    });
  };

  const downloadTemplate = () => {
    const csvContent =
      "name,email,password\nPremium Account 1,<EMAIL>,password123\nPremium Account 2,<EMAIL>,password456";
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "stock_template.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const products = productsData?.products || [];
  const canProceed =
    selectedVariantId && csvData?.data.length && csvData.errors.length === 0;

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        setOpen(newOpen);
        if (!newOpen) {
          resetForm();
        }
      }}
    >
      <DialogTrigger asChild>
        <Button variant="outline">
          <Upload className="h-4 w-4" />
          Bulk Upload
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Bulk Upload Stocks</DialogTitle>
          <DialogDescription>
            Upload multiple stock items from a CSV file. Download the template
            to see the required format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium">CSV Template</h4>
              <p className="text-sm text-muted-foreground">
                Download the template to see the required format
              </p>
            </div>
            <Button variant="outline" size="sm" onClick={downloadTemplate}>
              <Download className="h-4 w-4" />
              Download Template
            </Button>
          </div>

          {/* Product and Variant Selection */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Product</Label>
              <Select
                value={selectedProductId}
                onValueChange={(value) => {
                  setSelectedProductId(value);
                  setSelectedVariantId(""); // Reset variant when product changes
                }}
                disabled={isLoadingProducts}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Variant</Label>
              <Select
                value={selectedVariantId}
                onValueChange={setSelectedVariantId}
                disabled={!selectedProductId || isLoadingVariants}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a variant" />
                </SelectTrigger>
                <SelectContent>
                  {variants?.map((variant) => (
                    <SelectItem key={variant.id} value={variant.id}>
                      {variant.name} - ${variant.price}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label>Upload CSV File</Label>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".csv,.txt"
              onChange={handleFileUpload}
              disabled={isUploading}
            />
            {isUploading && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Parsing file...
              </div>
            )}
          </div>

          {/* Preview and Errors */}
          {csvData && (
            <div className="space-y-4">
              {/* Errors */}
              {csvData.errors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">
                        Found {csvData.errors.length} errors:
                      </p>
                      {csvData.errors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-sm">
                          Row {error.row}: {error.errors.join(", ")}
                        </p>
                      ))}
                      {csvData.errors.length > 5 && (
                        <p className="text-sm">
                          ... and {csvData.errors.length - 5} more errors
                        </p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Preview Table */}
              {csvData.data.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">
                    Preview ({csvData.data.length} valid rows)
                  </h4>
                  <div className="border rounded-lg max-h-60 overflow-y-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Password</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {csvData.data.slice(0, 10).map((row, index) => (
                          <TableRow key={index}>
                            <TableCell>{row.name}</TableCell>
                            <TableCell>{row.email}</TableCell>
                            <TableCell>
                              {"*".repeat(row.password.length)}
                            </TableCell>
                          </TableRow>
                        ))}
                        {csvData.data.length > 10 && (
                          <TableRow>
                            <TableCell
                              colSpan={3}
                              className="text-center text-muted-foreground"
                            >
                              ... and {csvData.data.length - 10} more rows
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={createBulkStockMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpload}
            disabled={!canProceed || createBulkStockMutation.isPending}
          >
            {createBulkStockMutation.isPending && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
            Create {csvData?.data.length || 0} Stocks
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
