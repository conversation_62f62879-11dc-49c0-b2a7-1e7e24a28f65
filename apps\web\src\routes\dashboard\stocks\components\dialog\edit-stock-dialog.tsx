import { useState, useEffect } from "react";
import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { Edit, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { trpcClient, queryClient } from "@/utils/trpc";
import type { Stock } from "../table/table-columns";

// Form validation schema
const updateStockSchema = z.object({
  name: z.string().min(1, "Account name is required"),
  email: z.string().email("Valid email is required"),
  password: z.string().min(1, "Password is required"),
});

type UpdateStockFormData = z.infer<typeof updateStockSchema>;

interface EditStockDialogProps {
  stock: Stock;
  onSuccess?: () => void;
  children?: React.ReactNode;
}

export function EditStockDialog({ stock, onSuccess, children }: EditStockDialogProps) {
  const [open, setOpen] = useState(false);

  const updateStockMutation = useMutation({
    mutationFn: async (data: UpdateStockFormData) => {
      return trpcClient.stock.update.mutate({
        id: stock.id,
        name: data.name,
        email: data.email,
        password: data.password,
      });
    },
    onSuccess: () => {
      toast.success("Stock updated successfully!");
      setOpen(false);
      
      // Invalidate and refetch stocks
      queryClient.invalidateQueries({
        queryKey: [["stock", "getAll"]],
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update stock");
    },
  });

  const form = useForm({
    defaultValues: {
      name: stock.name,
      email: stock.email,
      password: stock.password,
    } as UpdateStockFormData,
    onSubmit: async ({ value }) => {
      await updateStockMutation.mutateAsync(value);
    },
    validators: {
      onChange: updateStockSchema,
    },
  });

  // Reset form when dialog opens with fresh stock data
  useEffect(() => {
    if (open) {
      form.setFieldValue("name", stock.name);
      form.setFieldValue("email", stock.email);
      form.setFieldValue("password", stock.password);
    }
  }, [open, stock, form]);

  // Check if stock can be edited (only AVAILABLE stocks can be edited)
  const canEdit = stock.status === "AVAILABLE";

  const trigger = children || (
    <Button
      variant="ghost"
      size="sm"
      disabled={!canEdit}
      className="h-8 w-8 p-0"
    >
      <Edit className="h-4 w-4" />
    </Button>
  );

  if (!canEdit) {
    return (
      <div className="relative">
        {trigger}
        <span className="sr-only">Cannot edit {stock.status.toLowerCase()} stock</span>
      </div>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Stock</DialogTitle>
          <DialogDescription>
            Update the account credentials for this stock item.
            <br />
            <span className="text-sm text-muted-foreground">
              Stock ID: {stock.id} | Product: {stock.variant.product.name} | Variant: {stock.variant.name}
            </span>
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          {/* Account Name */}
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Account Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., Premium Account 1"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Email */}
          <div>
            <form.Field name="email">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Email</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="email"
                    placeholder="e.g., <EMAIL>"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Password */}
          <div>
            <form.Field name="password">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Password</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="password"
                    placeholder="Enter account password"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={updateStockMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={updateStockMutation.isPending}
            >
              {updateStockMutation.isPending && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              Update Stock
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
