import { useState, useEffect } from "react";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { PlusIcon, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { trpc, trpcClient, queryClient } from "@/utils/trpc";

// Form validation schema
const createStockSchema = z.object({
  name: z.string().min(1, "Account name is required"),
  email: z.string().email("Valid email is required"),
  password: z.string().min(1, "Password is required"),
  productId: z.string().min(1, "Product is required"),
  variantId: z.string().min(1, "Variant is required"),
});

type CreateStockFormData = z.infer<typeof createStockSchema>;

interface AddStockDialogProps {
  onSuccess?: () => void;
}

export function AddStockDialog({ onSuccess }: AddStockDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string>("");

  // Fetch products for dropdown
  const { data: productsData, isLoading: isLoadingProducts } = useQuery(
    trpc.product.getAll.queryOptions({
      page: 1,
      limit: 100, // Get all products for dropdown
    })
  );

  // Fetch variants based on selected product
  const { data: variants, isLoading: isLoadingVariants } = useQuery(
    trpc.variant.getByProductId.queryOptions(
      { productId: selectedProductId },
      { enabled: !!selectedProductId }
    )
  );

  const createStockMutation = useMutation({
    mutationFn: async (data: CreateStockFormData) => {
      return trpcClient.stock.create.mutate({
        name: data.name,
        email: data.email,
        password: data.password,
        variantId: data.variantId,
      });
    },
    onSuccess: () => {
      toast.success("Stock created successfully!");
      setOpen(false);
      form.reset();
      setSelectedProductId("");
      
      // Invalidate and refetch stocks
      queryClient.invalidateQueries({
        queryKey: [["stock", "getAll"]],
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create stock");
    },
  });

  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      password: "",
      productId: "",
      variantId: "",
    } as CreateStockFormData,
    onSubmit: async ({ value }) => {
      await createStockMutation.mutateAsync(value);
    },
    validators: {
      onChange: createStockSchema,
    },
  });

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      form.reset();
      setSelectedProductId("");
    }
  }, [open, form]);

  const products = productsData?.products || [];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <PlusIcon className="h-4 w-4" />
          Add Stock
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add New Stock</DialogTitle>
          <DialogDescription>
            Create a new stock item by providing account credentials and selecting the product variant.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          {/* Product Selection */}
          <div>
            <form.Field name="productId">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Product</Label>
                  <Select
                    value={field.state.value}
                    onValueChange={(value) => {
                      field.handleChange(value);
                      setSelectedProductId(value);
                      // Reset variant selection when product changes
                      form.setFieldValue("variantId", "");
                    }}
                    disabled={isLoadingProducts}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Variant Selection */}
          <div>
            <form.Field name="variantId">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Variant</Label>
                  <Select
                    value={field.state.value}
                    onValueChange={field.handleChange}
                    disabled={!selectedProductId || isLoadingVariants}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a variant" />
                    </SelectTrigger>
                    <SelectContent>
                      {variants?.map((variant) => (
                        <SelectItem key={variant.id} value={variant.id}>
                          {variant.name} - ${variant.price}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Account Name */}
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Account Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., Premium Account 1"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Email */}
          <div>
            <form.Field name="email">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Email</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="email"
                    placeholder="e.g., <EMAIL>"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          {/* Password */}
          <div>
            <form.Field name="password">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Password</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="password"
                    placeholder="Enter account password"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p key={error?.message} className="text-sm text-destructive">
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={createStockMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createStockMutation.isPending}
            >
              {createStockMutation.isPending && (
                <Loader2 className="h-4 w-4 animate-spin" />
              )}
              Create Stock
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
